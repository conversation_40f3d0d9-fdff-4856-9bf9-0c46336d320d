from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
import uuid

User = get_user_model()


class InvoiceTemplate(models.Model):
    """Model for storing default invoice template designs"""

    TEMPLATE_TYPES = [
        ("minimalist", "Modern Minimalist"),
        ("corporate", "Corporate Professional"),
        ("contemporary", "Creative Contemporary"),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100)
    template_type = models.CharField(max_length=20, choices=TEMPLATE_TYPES, unique=True)
    description = models.TextField()

    # Template structure stored as JSON
    layout_config = models.JSONField(
        default=dict, help_text="Template layout configuration"
    )
    style_config = models.JSONField(
        default=dict, help_text="Template styling configuration"
    )

    # Template preview image
    preview_image = models.ImageField(
        upload_to="template_previews/", null=True, blank=True
    )

    # Metadata
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["template_type"]

    def __str__(self):
        return f"{self.name} ({self.get_template_type_display()})"


class CompanyProfile(models.Model):
    """Model for storing user's company information"""

    user = models.OneToOneField(
        User, on_delete=models.CASCADE, related_name="company_profile"
    )

    # Basic company information
    company_name = models.CharField(max_length=200)
    address_line_1 = models.CharField(max_length=200, blank=True)
    address_line_2 = models.CharField(max_length=200, blank=True)
    city = models.CharField(max_length=100, blank=True)
    state_province = models.CharField(max_length=100, blank=True)
    postal_code = models.CharField(max_length=20, blank=True)
    country = models.CharField(max_length=100, blank=True)

    # Contact information
    phone = models.CharField(max_length=20, blank=True)
    email = models.EmailField(blank=True)
    website = models.URLField(blank=True)

    # Business details
    tax_id = models.CharField(
        max_length=50, blank=True, help_text="Tax ID, VAT number, or EIN"
    )
    business_registration = models.CharField(max_length=100, blank=True)

    # Banking information
    bank_name = models.CharField(max_length=100, blank=True)
    account_number = models.CharField(max_length=50, blank=True)
    routing_number = models.CharField(max_length=20, blank=True)
    swift_code = models.CharField(max_length=20, blank=True)

    # Payment terms
    default_payment_terms = models.CharField(max_length=200, default="Net 30 days")

    # Company logo
    logo = models.ImageField(upload_to="company_logos/", null=True, blank=True)

    # Metadata
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.company_name} - {self.user.email}"


class CustomTemplate(models.Model):
    """Model for storing user's customized invoice templates"""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="custom_templates"
    )

    # Template details
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)

    # Base template reference
    base_template = models.ForeignKey(
        InvoiceTemplate, on_delete=models.CASCADE, related_name="custom_variants"
    )

    # Customized configuration
    custom_layout_config = models.JSONField(
        default=dict, help_text="Customized layout configuration"
    )
    custom_style_config = models.JSONField(
        default=dict, help_text="Customized styling configuration"
    )

    # Company information snapshot (for this template)
    company_info = models.JSONField(
        default=dict, help_text="Company information used in this template"
    )

    # Template preview
    preview_image = models.ImageField(
        upload_to="custom_template_previews/", null=True, blank=True
    )

    # Usage tracking
    usage_count = models.PositiveIntegerField(default=0)
    last_used = models.DateTimeField(null=True, blank=True)

    # Metadata
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["-last_used", "-created_at"]
        unique_together = ["user", "name"]

    def __str__(self):
        return f"{self.name} - {self.user.email}"

    def mark_as_used(self):
        """Mark template as used and increment usage count"""
        self.last_used = timezone.now()
        self.usage_count += 1
        self.save(update_fields=["last_used", "usage_count"])
