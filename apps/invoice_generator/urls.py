from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import (
    extract_template_information,
    InvoiceTemplateViewSet,
    CustomTemplateViewSet,
    company_profile_view,
    generate_template_preview,
)

# Create router for ViewSets
router = DefaultRouter()
router.register(r"templates", InvoiceTemplateViewSet, basename="invoice-template")
router.register(r"custom-templates", CustomTemplateViewSet, basename="custom-template")

urlpatterns = [
    # Include router URLs
    path("", include(router.urls)),
    # Individual endpoints
    path(
        "extract-template-information/",
        extract_template_information,
        name="extract_template_information",
    ),
    path(
        "company-profile/",
        company_profile_view,
        name="company_profile",
    ),
    path(
        "generate-preview/",
        generate_template_preview,
        name="generate_template_preview",
    ),
]
