from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>outer
from .views import (
    company_profile_view,
    CustomTemplateViewSet,
    template_preview_html,
    InvoiceTemplateViewSet,
    generate_template_preview,
    template_list_with_previews,
    extract_template_information,
)

# Create router for ViewSets
router = DefaultRouter()
router.register(r"templates", InvoiceTemplateViewSet, basename="invoice-template")
router.register(r"custom-templates", CustomTemplateViewSet, basename="custom-template")

urlpatterns = [
    # Include router URLs
    path("", include(router.urls)),
    # Individual endpoints
    path(
        "extract-template-information/",
        extract_template_information,
        name="extract_template_information",
    ),
    path(
        "company-profile/",
        company_profile_view,
        name="company_profile",
    ),
    path(
        "generate-preview/",
        generate_template_preview,
        name="generate_template_preview",
    ),
    path(
        "preview/<uuid:template_id>/",
        template_preview_html,
        name="template_preview_html",
    ),
    path(
        "templates-with-previews/",
        template_list_with_previews,
        name="template_list_with_previews",
    ),
]
