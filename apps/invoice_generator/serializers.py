from rest_framework import serializers
from .models import InvoiceTemplate, CompanyProfile, CustomTemplate


class InvoiceTemplateSerializer(serializers.ModelSerializer):
    """Serializer for default invoice templates"""
    
    template_type_display = serializers.CharField(source='get_template_type_display', read_only=True)
    
    class Meta:
        model = InvoiceTemplate
        fields = [
            'id', 'name', 'template_type', 'template_type_display', 
            'description', 'layout_config', 'style_config', 
            'preview_image', 'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class CompanyProfileSerializer(serializers.ModelSerializer):
    """Serializer for company profile information"""
    
    class Meta:
        model = CompanyProfile
        fields = [
            'company_name', 'address_line_1', 'address_line_2', 
            'city', 'state_province', 'postal_code', 'country',
            'phone', 'email', 'website', 'tax_id', 'business_registration',
            'bank_name', 'account_number', 'routing_number', 'swift_code',
            'default_payment_terms', 'logo', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def validate_email(self, value):
        """Validate email format if provided"""
        if value and '@' not in value:
            raise serializers.ValidationError("Please enter a valid email address.")
        return value


class CustomTemplateSerializer(serializers.ModelSerializer):
    """Serializer for custom user templates"""
    
    base_template_name = serializers.CharField(source='base_template.name', read_only=True)
    base_template_type = serializers.CharField(source='base_template.template_type', read_only=True)
    
    class Meta:
        model = CustomTemplate
        fields = [
            'id', 'name', 'description', 'base_template', 'base_template_name', 
            'base_template_type', 'custom_layout_config', 'custom_style_config',
            'company_info', 'preview_image', 'usage_count', 'last_used',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'usage_count', 'last_used', 'created_at', 'updated_at']
    
    def validate_name(self, value):
        """Validate template name uniqueness for the user"""
        user = self.context['request'].user
        if CustomTemplate.objects.filter(user=user, name=value).exclude(pk=self.instance.pk if self.instance else None).exists():
            raise serializers.ValidationError("A template with this name already exists.")
        return value


class CustomTemplateCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating custom templates"""
    
    class Meta:
        model = CustomTemplate
        fields = [
            'name', 'description', 'base_template', 'custom_layout_config', 
            'custom_style_config', 'company_info'
        ]
    
    def validate_name(self, value):
        """Validate template name uniqueness for the user"""
        user = self.context['request'].user
        if CustomTemplate.objects.filter(user=user, name=value).exists():
            raise serializers.ValidationError("A template with this name already exists.")
        return value
    
    def create(self, validated_data):
        """Create custom template with user assignment"""
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)


class TemplatePreviewSerializer(serializers.Serializer):
    """Serializer for template preview generation"""
    
    template_id = serializers.UUIDField()
    company_info = serializers.JSONField()
    custom_layout_config = serializers.JSONField(required=False)
    custom_style_config = serializers.JSONField(required=False)
