from django.core.management.base import BaseCommand
from apps.invoice_generator.models import InvoiceTemplate


class Command(BaseCommand):
    help = 'Create default invoice templates'

    def handle(self, *args, **options):
        templates = [
            {
                'name': 'Modern Minimalist',
                'template_type': 'minimalist',
                'description': 'Clean, modern design with plenty of white space and subtle typography. Perfect for contemporary businesses.',
                'layout_config': {
                    'header': {
                        'layout': 'split',
                        'logo_position': 'left',
                        'company_info_position': 'right',
                        'height': '120px'
                    },
                    'client_section': {
                        'position': 'left',
                        'width': '50%',
                        'margin_top': '40px'
                    },
                    'invoice_meta': {
                        'position': 'right',
                        'width': '40%',
                        'margin_top': '40px'
                    },
                    'line_items': {
                        'margin_top': '60px',
                        'table_style': 'minimal'
                    },
                    'totals': {
                        'position': 'right',
                        'width': '40%',
                        'margin_top': '20px'
                    },
                    'footer': {
                        'margin_top': '60px',
                        'text_align': 'center'
                    }
                },
                'style_config': {
                    'colors': {
                        'primary': '#2563eb',
                        'secondary': '#64748b',
                        'text': '#1e293b',
                        'border': '#e2e8f0',
                        'background': '#ffffff'
                    },
                    'fonts': {
                        'primary': 'Inter, sans-serif',
                        'secondary': 'Inter, sans-serif'
                    },
                    'spacing': {
                        'section_gap': '40px',
                        'line_height': '1.6'
                    }
                }
            },
            {
                'name': 'Corporate Professional',
                'template_type': 'corporate',
                'description': 'Traditional business layout with structured sections and professional styling. Ideal for established companies.',
                'layout_config': {
                    'header': {
                        'layout': 'stacked',
                        'logo_position': 'center',
                        'company_info_position': 'center',
                        'height': '140px'
                    },
                    'client_section': {
                        'position': 'left',
                        'width': '45%',
                        'margin_top': '30px'
                    },
                    'invoice_meta': {
                        'position': 'right',
                        'width': '45%',
                        'margin_top': '30px'
                    },
                    'line_items': {
                        'margin_top': '40px',
                        'table_style': 'bordered'
                    },
                    'totals': {
                        'position': 'right',
                        'width': '45%',
                        'margin_top': '20px'
                    },
                    'footer': {
                        'margin_top': '40px',
                        'text_align': 'left'
                    }
                },
                'style_config': {
                    'colors': {
                        'primary': '#1f2937',
                        'secondary': '#6b7280',
                        'text': '#111827',
                        'border': '#d1d5db',
                        'background': '#ffffff'
                    },
                    'fonts': {
                        'primary': 'Times New Roman, serif',
                        'secondary': 'Arial, sans-serif'
                    },
                    'spacing': {
                        'section_gap': '30px',
                        'line_height': '1.5'
                    }
                }
            },
            {
                'name': 'Creative Contemporary',
                'template_type': 'contemporary',
                'description': 'Modern design with accent colors and visual elements. Great for creative agencies and modern businesses.',
                'layout_config': {
                    'header': {
                        'layout': 'asymmetric',
                        'logo_position': 'left',
                        'company_info_position': 'right',
                        'height': '100px',
                        'accent_bar': True
                    },
                    'client_section': {
                        'position': 'left',
                        'width': '48%',
                        'margin_top': '50px'
                    },
                    'invoice_meta': {
                        'position': 'right',
                        'width': '48%',
                        'margin_top': '50px'
                    },
                    'line_items': {
                        'margin_top': '50px',
                        'table_style': 'modern'
                    },
                    'totals': {
                        'position': 'right',
                        'width': '45%',
                        'margin_top': '25px'
                    },
                    'footer': {
                        'margin_top': '50px',
                        'text_align': 'center'
                    }
                },
                'style_config': {
                    'colors': {
                        'primary': '#7c3aed',
                        'secondary': '#a855f7',
                        'text': '#1f2937',
                        'border': '#e5e7eb',
                        'background': '#ffffff',
                        'accent': '#f3f4f6'
                    },
                    'fonts': {
                        'primary': 'Poppins, sans-serif',
                        'secondary': 'Open Sans, sans-serif'
                    },
                    'spacing': {
                        'section_gap': '50px',
                        'line_height': '1.7'
                    }
                }
            }
        ]

        created_count = 0
        for template_data in templates:
            template, created = InvoiceTemplate.objects.get_or_create(
                template_type=template_data['template_type'],
                defaults=template_data
            )
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created template: {template.name}')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'Template already exists: {template.name}')
                )

        self.stdout.write(
            self.style.SUCCESS(f'Successfully created {created_count} new templates')
        )
