# Generated by Django 5.2 on 2025-05-28 23:13

import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='InvoiceTemplate',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100)),
                ('template_type', models.CharField(choices=[('minimalist', 'Modern Minimalist'), ('corporate', 'Corporate Professional'), ('contemporary', 'Creative Contemporary')], max_length=20, unique=True)),
                ('description', models.TextField()),
                ('layout_config', models.JSONField(default=dict, help_text='Template layout configuration')),
                ('style_config', models.J<PERSON><PERSON>ield(default=dict, help_text='Template styling configuration')),
                ('preview_image', models.ImageField(blank=True, null=True, upload_to='template_previews/')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['template_type'],
            },
        ),
        migrations.CreateModel(
            name='CompanyProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('company_name', models.CharField(max_length=200)),
                ('address_line_1', models.CharField(blank=True, max_length=200)),
                ('address_line_2', models.CharField(blank=True, max_length=200)),
                ('city', models.CharField(blank=True, max_length=100)),
                ('state_province', models.CharField(blank=True, max_length=100)),
                ('postal_code', models.CharField(blank=True, max_length=20)),
                ('country', models.CharField(blank=True, max_length=100)),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('email', models.EmailField(blank=True, max_length=254)),
                ('website', models.URLField(blank=True)),
                ('tax_id', models.CharField(blank=True, help_text='Tax ID, VAT number, or EIN', max_length=50)),
                ('business_registration', models.CharField(blank=True, max_length=100)),
                ('bank_name', models.CharField(blank=True, max_length=100)),
                ('account_number', models.CharField(blank=True, max_length=50)),
                ('routing_number', models.CharField(blank=True, max_length=20)),
                ('swift_code', models.CharField(blank=True, max_length=20)),
                ('default_payment_terms', models.CharField(default='Net 30 days', max_length=200)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='company_logos/')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='company_profile', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='CustomTemplate',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('custom_layout_config', models.JSONField(default=dict, help_text='Customized layout configuration')),
                ('custom_style_config', models.JSONField(default=dict, help_text='Customized styling configuration')),
                ('company_info', models.JSONField(default=dict, help_text='Company information used in this template')),
                ('preview_image', models.ImageField(blank=True, null=True, upload_to='custom_template_previews/')),
                ('usage_count', models.PositiveIntegerField(default=0)),
                ('last_used', models.DateTimeField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='custom_templates', to=settings.AUTH_USER_MODEL)),
                ('base_template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='custom_variants', to='invoice_generator.invoicetemplate')),
            ],
            options={
                'ordering': ['-last_used', '-created_at'],
                'unique_together': {('user', 'name')},
            },
        ),
    ]
