from django.template.loader import render_to_string
from django.template import Context, Template
from django.conf import settings
import os
from datetime import datetime, timedelta
from decimal import Decimal


def get_template_file_mapping():
    """Map template types to their corresponding HTML files"""
    return {
        'minimalist': 'invoice_generator/minimalist.html',
        'corporate': 'invoice_generator/corporate.html',
        'contemporary': 'invoice_generator/contemporary.html',
        'clean_business': 'invoice_generator/clean_business.html',
        'elegant_classic': 'invoice_generator/elegant_classic.html',
    }


def generate_sample_invoice_data():
    """Generate sample invoice data for preview purposes"""
    today = datetime.now()
    due_date = today + timedelta(days=30)
    
    return {
        'invoice': {
            'number': 'INV-2024-001',
            'date': today.strftime('%B %d, %Y'),
            'due_date': due_date.strftime('%B %d, %Y'),
            'po_number': 'PO-12345',
            'subtotal': Decimal('1200.00'),
            'tax_rate': '8.5',
            'tax_amount': Decimal('102.00'),
            'discount_amount': Decimal('50.00'),
            'total': Decimal('1252.00'),
            'items': [
                {
                    'description': 'Web Development Services',
                    'quantity': 40,
                    'rate': Decimal('25.00'),
                    'amount': Decimal('1000.00')
                },
                {
                    'description': 'UI/UX Design Consultation',
                    'quantity': 8,
                    'rate': Decimal('30.00'),
                    'amount': Decimal('240.00')
                }
            ]
        },
        'client': {
            'name': 'Acme Corporation',
            'address': '123 Business Street\nSuite 100\nNew York, NY 10001',
            'email': '<EMAIL>',
            'phone': '+****************'
        },
        'company': {
            'company_name': 'Your Company Name',
            'address_line_1': '456 Professional Ave',
            'address_line_2': 'Floor 5',
            'city': 'San Francisco',
            'state_province': 'CA',
            'postal_code': '94102',
            'country': 'United States',
            'phone': '+****************',
            'email': '<EMAIL>',
            'website': 'www.yourcompany.com',
            'tax_id': 'TAX-*********',
            'business_registration': 'REG-*********',
            'bank_name': 'First National Bank',
            'account_number': '****1234',
            'routing_number': '*********',
            'swift_code': 'FNBKUS33',
            'default_payment_terms': 'Net 30 days',
            'logo': None  # Will be populated if available
        }
    }


def render_invoice_template(template_type, company_data=None, invoice_data=None, client_data=None):
    """
    Render an invoice template with provided data
    
    Args:
        template_type (str): The template type (minimalist, corporate, etc.)
        company_data (dict): Company information
        invoice_data (dict): Invoice details
        client_data (dict): Client information
    
    Returns:
        str: Rendered HTML content
    """
    template_mapping = get_template_file_mapping()
    
    if template_type not in template_mapping:
        raise ValueError(f"Unknown template type: {template_type}")
    
    template_file = template_mapping[template_type]
    
    # Use sample data if not provided
    if not company_data or not invoice_data or not client_data:
        sample_data = generate_sample_invoice_data()
        company_data = company_data or sample_data['company']
        invoice_data = invoice_data or sample_data['invoice']
        client_data = client_data or sample_data['client']
    
    # Merge company data with any custom overrides
    if company_data:
        sample_company = generate_sample_invoice_data()['company']
        sample_company.update(company_data)
        company_data = sample_company
    
    context = {
        'company': company_data,
        'invoice': invoice_data,
        'client': client_data,
    }
    
    try:
        return render_to_string(template_file, context)
    except Exception as e:
        raise Exception(f"Error rendering template {template_type}: {str(e)}")


def generate_template_preview_html(template_id, company_info=None):
    """
    Generate HTML preview for a specific template
    
    Args:
        template_id (str): Template ID or type
        company_info (dict): Optional company information to override defaults
    
    Returns:
        str: Rendered HTML content
    """
    from .models import InvoiceTemplate
    
    try:
        # Get template from database
        template = InvoiceTemplate.objects.get(id=template_id, is_active=True)
        template_type = template.template_type
    except InvoiceTemplate.DoesNotExist:
        # Fallback to using template_id as template_type
        template_type = template_id
    
    # Generate sample data
    sample_data = generate_sample_invoice_data()
    
    # Override company data if provided
    if company_info:
        sample_data['company'].update(company_info)
    
    return render_invoice_template(
        template_type=template_type,
        company_data=sample_data['company'],
        invoice_data=sample_data['invoice'],
        client_data=sample_data['client']
    )


def get_template_preview_data(template_type):
    """
    Get preview data for a specific template type
    
    Args:
        template_type (str): The template type
    
    Returns:
        dict: Template preview information
    """
    template_mapping = get_template_file_mapping()
    
    if template_type not in template_mapping:
        return None
    
    # Generate sample HTML
    try:
        html_content = render_invoice_template(template_type)
        return {
            'template_type': template_type,
            'html_content': html_content,
            'success': True
        }
    except Exception as e:
        return {
            'template_type': template_type,
            'error': str(e),
            'success': False
        }


def validate_template_data(template_data):
    """
    Validate template data structure
    
    Args:
        template_data (dict): Template data to validate
    
    Returns:
        tuple: (is_valid, errors)
    """
    errors = []
    
    required_sections = ['company', 'invoice', 'client']
    for section in required_sections:
        if section not in template_data:
            errors.append(f"Missing required section: {section}")
    
    # Validate company data
    if 'company' in template_data:
        company = template_data['company']
        if not company.get('company_name'):
            errors.append("Company name is required")
    
    # Validate invoice data
    if 'invoice' in template_data:
        invoice = template_data['invoice']
        required_invoice_fields = ['number', 'date', 'items']
        for field in required_invoice_fields:
            if not invoice.get(field):
                errors.append(f"Invoice {field} is required")
        
        # Validate items
        if 'items' in invoice and isinstance(invoice['items'], list):
            for i, item in enumerate(invoice['items']):
                required_item_fields = ['description', 'quantity', 'rate', 'amount']
                for field in required_item_fields:
                    if field not in item:
                        errors.append(f"Item {i+1} missing {field}")
    
    # Validate client data
    if 'client' in template_data:
        client = template_data['client']
        if not client.get('name'):
            errors.append("Client name is required")
    
    return len(errors) == 0, errors


def calculate_invoice_totals(items, tax_rate=0, discount_amount=0):
    """
    Calculate invoice totals from line items
    
    Args:
        items (list): List of invoice items
        tax_rate (float): Tax rate as percentage
        discount_amount (float): Discount amount
    
    Returns:
        dict: Calculated totals
    """
    subtotal = sum(Decimal(str(item.get('amount', 0))) for item in items)
    tax_amount = subtotal * Decimal(str(tax_rate)) / 100
    total = subtotal + tax_amount - Decimal(str(discount_amount))
    
    return {
        'subtotal': subtotal,
        'tax_amount': tax_amount,
        'discount_amount': Decimal(str(discount_amount)),
        'total': total
    }
