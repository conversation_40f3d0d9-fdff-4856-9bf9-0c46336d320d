"use client";

import { useState } from "react";
import { motion } from "framer-motion";

const PropertyPanel = ({
  selectedComponent,
  template,
  onUpdateComponent,
  onUpdateTemplate
}) => {
  const [activeTab, setActiveTab] = useState("style");

  if (!selectedComponent) {
    return (
      <div className="w-80 bg-white border-l border-gray-200 flex flex-col">
        <div className="p-4 border-b border-gray-200">
          <h3 className="font-semibold text-gray-800">Properties</h3>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center text-gray-500">
            <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.122 2.122" />
              </svg>
            </div>
            <p className="text-sm">Select a component to edit its properties</p>
          </div>
        </div>
      </div>
    );
  }

  const handleStyleChange = (property, value) => {
    onUpdateComponent(selectedComponent.id, {
      style: {
        ...selectedComponent.style,
        [property]: value
      }
    });
  };

  const handleContentChange = (value) => {
    onUpdateComponent(selectedComponent.id, {
      content: value
    });
  };

  const handlePositionChange = (property, value) => {
    onUpdateComponent(selectedComponent.id, {
      position: {
        ...selectedComponent.position,
        [property]: parseFloat(value) || 0
      }
    });
  };

  const handleSizeChange = (property, value) => {
    onUpdateComponent(selectedComponent.id, {
      size: {
        ...selectedComponent.size,
        [property]: parseFloat(value) || 0
      }
    });
  };

  const tabs = [
    { id: "style", name: "Style", icon: "🎨" },
    { id: "content", name: "Content", icon: "📝" },
    { id: "layout", name: "Layout", icon: "📐" },
    { id: "advanced", name: "Advanced", icon: "⚙️" }
  ];

  return (
    <div className="w-80 bg-white border-l border-gray-200 flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <h3 className="font-semibold text-gray-800">Properties</h3>
        <p className="text-sm text-gray-600 capitalize">{selectedComponent.type} Component</p>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <div className="flex">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              className={`flex-1 px-3 py-2 text-sm font-medium border-b-2 transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600 bg-blue-50'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActiveTab(tab.id)}
            >
              <span className="mr-1">{tab.icon}</span>
              {tab.name}
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4">
        {activeTab === "style" && (
          <div className="space-y-4">
            {/* Typography */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Font Family</label>
              <select
                value={selectedComponent.style.fontFamily || "Inter"}
                onChange={(e) => handleStyleChange("fontFamily", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="Inter">Inter</option>
                <option value="Arial">Arial</option>
                <option value="Helvetica">Helvetica</option>
                <option value="Times New Roman">Times New Roman</option>
                <option value="Georgia">Georgia</option>
                <option value="Courier New">Courier New</option>
              </select>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Font Size</label>
                <input
                  type="number"
                  value={selectedComponent.style.fontSize || 12}
                  onChange={(e) => handleStyleChange("fontSize", parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                  min="8"
                  max="72"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Font Weight</label>
                <select
                  value={selectedComponent.style.fontWeight || "normal"}
                  onChange={(e) => handleStyleChange("fontWeight", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                >
                  <option value="normal">Normal</option>
                  <option value="bold">Bold</option>
                  <option value="600">Semi Bold</option>
                  <option value="300">Light</option>
                </select>
              </div>
            </div>

            {/* Colors */}
            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Text Color</label>
                <div className="flex items-center space-x-2">
                  <input
                    type="color"
                    value={selectedComponent.style.color || "#000000"}
                    onChange={(e) => handleStyleChange("color", e.target.value)}
                    className="w-8 h-8 border border-gray-300 rounded"
                  />
                  <input
                    type="text"
                    value={selectedComponent.style.color || "#000000"}
                    onChange={(e) => handleStyleChange("color", e.target.value)}
                    className="flex-1 px-2 py-1 border border-gray-300 rounded text-sm"
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Background</label>
                <div className="flex items-center space-x-2">
                  <input
                    type="color"
                    value={selectedComponent.style.backgroundColor || "#ffffff"}
                    onChange={(e) => handleStyleChange("backgroundColor", e.target.value)}
                    className="w-8 h-8 border border-gray-300 rounded"
                  />
                  <input
                    type="text"
                    value={selectedComponent.style.backgroundColor || "#ffffff"}
                    onChange={(e) => handleStyleChange("backgroundColor", e.target.value)}
                    className="flex-1 px-2 py-1 border border-gray-300 rounded text-sm"
                  />
                </div>
              </div>
            </div>

            {/* Text Alignment */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Text Align</label>
              <div className="flex space-x-1">
                {["left", "center", "right", "justify"].map((align) => (
                  <button
                    key={align}
                    className={`flex-1 px-3 py-2 text-sm border rounded ${
                      selectedComponent.style.textAlign === align
                        ? 'bg-blue-500 text-white border-blue-500'
                        : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                    }`}
                    onClick={() => handleStyleChange("textAlign", align)}
                  >
                    {align.charAt(0).toUpperCase() + align.slice(1)}
                  </button>
                ))}
              </div>
            </div>

            {/* Padding */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Padding</label>
              <input
                type="number"
                value={selectedComponent.style.padding || 8}
                onChange={(e) => handleStyleChange("padding", parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                min="0"
                max="50"
              />
            </div>

            {/* Border */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Border</label>
              <input
                type="text"
                value={selectedComponent.style.border || "none"}
                onChange={(e) => handleStyleChange("border", e.target.value)}
                placeholder="e.g., 1px solid #000000"
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              />
            </div>
          </div>
        )}

        {activeTab === "content" && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Content</label>
              <textarea
                value={selectedComponent.content || ""}
                onChange={(e) => handleContentChange(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                rows="6"
                placeholder="Enter content or use template variables like {{company.name}}"
              />
            </div>
            
            <div className="bg-gray-50 p-3 rounded-md">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Available Variables</h4>
              <div className="text-xs text-gray-600 space-y-1">
                <div>{'{{company.name}}'} - Company name</div>
                <div>{'{{company.address}}'} - Company address</div>
                <div>{'{{invoice.number}}'} - Invoice number</div>
                <div>{'{{invoice.date}}'} - Invoice date</div>
                <div>{'{{client.name}}'} - Client name</div>
                <div>{'{{totals.total}}'} - Total amount</div>
              </div>
            </div>
          </div>
        )}

        {activeTab === "layout" && (
          <div className="space-y-4">
            {/* Position */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Position</label>
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-xs text-gray-500 mb-1">X (px)</label>
                  <input
                    type="number"
                    value={selectedComponent.position.x || 0}
                    onChange={(e) => handlePositionChange("x", e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-500 mb-1">Y (px)</label>
                  <input
                    type="number"
                    value={selectedComponent.position.y || 0}
                    onChange={(e) => handlePositionChange("y", e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                  />
                </div>
              </div>
            </div>

            {/* Size */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Size</label>
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-xs text-gray-500 mb-1">Width (px)</label>
                  <input
                    type="number"
                    value={selectedComponent.size.width || 200}
                    onChange={(e) => handleSizeChange("width", e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    min="50"
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-500 mb-1">Height (px)</label>
                  <input
                    type="number"
                    value={selectedComponent.size.height || 50}
                    onChange={(e) => handleSizeChange("height", e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    min="30"
                  />
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === "advanced" && (
          <div className="space-y-4">
            {/* Component Settings */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Visible</label>
                <input
                  type="checkbox"
                  checked={selectedComponent.visible !== false}
                  onChange={(e) => onUpdateComponent(selectedComponent.id, { visible: e.target.checked })}
                  className="rounded"
                />
              </div>
              
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Locked</label>
                <input
                  type="checkbox"
                  checked={selectedComponent.locked || false}
                  onChange={(e) => onUpdateComponent(selectedComponent.id, { locked: e.target.checked })}
                  className="rounded"
                />
              </div>
            </div>

            {/* Component ID */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Component ID</label>
              <input
                type="text"
                value={selectedComponent.id}
                readOnly
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm bg-gray-50"
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PropertyPanel;
