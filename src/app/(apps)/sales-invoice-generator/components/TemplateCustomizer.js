import { motion } from "framer-motion";
import { useState } from "react";
import { invoiceGeneratorAPI } from "../../../../services/api";

const TemplateCustomizer = ({ selectedTemplate, companyInfo, onSave, onBack, variants }) => {
  const [templateName, setTemplateName] = useState('');
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [showSaveDialog, setShowSaveDialog] = useState(false);

  const handleSaveTemplate = async () => {
    if (!templateName.trim()) {
      setError('Please enter a template name');
      return;
    }

    try {
      setSaving(true);
      setError('');

      const templateData = {
        name: templateName.trim(),
        description: `Custom ${selectedTemplate.name} template`,
        base_template: selectedTemplate.id,
        custom_layout_config: selectedTemplate.layout_config,
        custom_style_config: selectedTemplate.style_config,
        company_info: companyInfo
      };

      const response = await invoiceGeneratorAPI.createCustomTemplate(templateData);
      onSave(response.data);
    } catch (err) {
      console.error('Error saving template:', err);
      if (err.response?.data?.name) {
        setError(err.response.data.name[0]);
      } else {
        setError('Failed to save template. Please try again.');
      }
    } finally {
      setSaving(false);
    }
  };

  const handleContinueWithoutSaving = () => {
    onSave(null); // Continue without saving
  };

  return (
    <motion.div
      className="flex flex-col gap-6"
      variants={variants}
    >
      <div className="flex items-center justify-between mb-4">
        <button
          onClick={onBack}
          className="flex items-center text-blue-600 hover:text-blue-800 transition-colors"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          Back
        </button>
        <h2 className="text-xl font-medium text-gray-700">
          Template Ready
        </h2>
        <div className="w-20"></div>
      </div>

      <div className="text-center max-w-2xl mx-auto">
        <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-green-600 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 className="text-lg font-semibold text-green-800 mb-2">
            Your template is ready!
          </h3>
          <p className="text-green-700">
            Your <strong>{selectedTemplate.name}</strong> template has been customized with your company information.
          </p>
        </div>
      </div>

      {/* Template Preview */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-700 mb-4">Template Preview</h3>
        
        <div className="border border-gray-200 rounded-lg p-8 bg-gray-50">
          <div className="max-w-2xl mx-auto bg-white p-8 shadow-sm">
            {/* Header */}
            <div className="flex justify-between items-start mb-8">
              <div>
                {companyInfo.logo && (
                  <img src={companyInfo.logo} alt="Company Logo" className="h-16 mb-4" />
                )}
                <h4 className="font-bold text-xl text-gray-800">
                  {companyInfo.company_name}
                </h4>
                <div className="text-sm text-gray-600 mt-2">
                  {companyInfo.address_line_1 && <div>{companyInfo.address_line_1}</div>}
                  {companyInfo.address_line_2 && <div>{companyInfo.address_line_2}</div>}
                  {(companyInfo.city || companyInfo.state_province || companyInfo.postal_code) && (
                    <div>
                      {companyInfo.city}{companyInfo.city && companyInfo.state_province && ', '}{companyInfo.state_province} {companyInfo.postal_code}
                    </div>
                  )}
                  {companyInfo.country && <div>{companyInfo.country}</div>}
                  {companyInfo.phone && <div>Phone: {companyInfo.phone}</div>}
                  {companyInfo.email && <div>Email: {companyInfo.email}</div>}
                  {companyInfo.website && <div>Website: {companyInfo.website}</div>}
                </div>
              </div>
              <div className="text-right">
                <h4 className="font-bold text-2xl text-gray-800">INVOICE</h4>
                <div className="text-sm text-gray-600 mt-2">
                  <div>Invoice #: INV-2024-001</div>
                  <div>Date: {new Date().toLocaleDateString()}</div>
                  <div>Due Date: {new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString()}</div>
                </div>
              </div>
            </div>

            {/* Bill To Section */}
            <div className="mb-8">
              <h5 className="font-semibold text-gray-700 mb-2">Bill To:</h5>
              <div className="text-gray-600">
                <div>Sample Client Company</div>
                <div>123 Client Street</div>
                <div>Client City, State 12345</div>
              </div>
            </div>

            {/* Line Items Table */}
            <div className="mb-8">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b-2 border-gray-300">
                    <th className="text-left py-2 font-semibold text-gray-700">Description</th>
                    <th className="text-right py-2 font-semibold text-gray-700">Qty</th>
                    <th className="text-right py-2 font-semibold text-gray-700">Rate</th>
                    <th className="text-right py-2 font-semibold text-gray-700">Amount</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b border-gray-200">
                    <td className="py-2 text-gray-600">Sample Service 1</td>
                    <td className="py-2 text-right text-gray-600">1</td>
                    <td className="py-2 text-right text-gray-600">$500.00</td>
                    <td className="py-2 text-right text-gray-600">$500.00</td>
                  </tr>
                  <tr className="border-b border-gray-200">
                    <td className="py-2 text-gray-600">Sample Service 2</td>
                    <td className="py-2 text-right text-gray-600">2</td>
                    <td className="py-2 text-right text-gray-600">$250.00</td>
                    <td className="py-2 text-right text-gray-600">$500.00</td>
                  </tr>
                </tbody>
              </table>
            </div>

            {/* Totals */}
            <div className="flex justify-end mb-8">
              <div className="w-64">
                <div className="flex justify-between py-1">
                  <span className="text-gray-600">Subtotal:</span>
                  <span className="text-gray-600">$1,000.00</span>
                </div>
                <div className="flex justify-between py-1">
                  <span className="text-gray-600">Tax (10%):</span>
                  <span className="text-gray-600">$100.00</span>
                </div>
                <div className="flex justify-between py-2 border-t border-gray-300 font-semibold">
                  <span className="text-gray-800">Total:</span>
                  <span className="text-gray-800">$1,100.00</span>
                </div>
              </div>
            </div>

            {/* Payment Terms */}
            <div className="text-sm text-gray-600">
              <div className="font-semibold mb-1">Payment Terms:</div>
              <div>{companyInfo.default_payment_terms}</div>
              {companyInfo.bank_name && (
                <div className="mt-2">
                  <div className="font-semibold">Banking Information:</div>
                  <div>Bank: {companyInfo.bank_name}</div>
                  {companyInfo.account_number && <div>Account: {companyInfo.account_number}</div>}
                  {companyInfo.routing_number && <div>Routing: {companyInfo.routing_number}</div>}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Save Options */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-700 mb-4">Save Template</h3>
        <p className="text-gray-600 mb-4">
          Would you like to save this customized template for future use?
        </p>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        )}

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Template Name
            </label>
            <input
              type="text"
              value={templateName}
              onChange={(e) => setTemplateName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="My Custom Invoice Template"
            />
          </div>

          <div className="flex gap-4">
            <button
              onClick={handleSaveTemplate}
              disabled={saving || !templateName.trim()}
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {saving ? 'Saving...' : 'Save Template'}
            </button>
            <button
              onClick={handleContinueWithoutSaving}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Continue Without Saving
            </button>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default TemplateCustomizer;
