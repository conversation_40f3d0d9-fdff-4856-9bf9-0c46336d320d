import { API_ENDPOINTS } from '@/config/api';
import axios from 'axios';
import Cookies from 'js-cookie';

// Ensure HTTPS for production environments
const enforceHTTPS = (url) => {
  if (process.env.NODE_ENV === 'production' && url.startsWith('http:')) {
    return url.replace('http:', 'https:');
  }
  return url;
};

// Create a custom axios instance
const api = axios.create({
  baseURL: enforceHTTPS(API_ENDPOINTS.ME.replace('/users/me/', '')),
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add a request interceptor to add the auth token to each request
api.interceptors.request.use(
  (config) => {
    // Ensure HTTPS for API requests in production
    if (process.env.NODE_ENV === 'production' && config.url?.startsWith('http:')) {
      config.url = config.url.replace('http:', 'https:');
    }

    const token = Cookies.get('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add a response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // If error is 401 Unauthorized and not already retrying
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Try to refresh the token
        const refreshToken = Cookies.get('refreshToken');
        if (!refreshToken) {
          // If no refresh token, redirect to login
          window.location.href = '/login';
          return Promise.reject(error);
        }

        // Call the refresh token endpoint
        const response = await axios.post(
          enforceHTTPS(API_ENDPOINTS.REFRESH_TOKEN),
          { refresh: refreshToken }
        );

        const { access } = response.data;

        // Update the token
        Cookies.set('token', access, { expires: 1 });

        // Update the header for the original request
        originalRequest.headers.Authorization = `Bearer ${access}`;

        // Retry the original request
        return api(originalRequest);
      } catch (refreshError) {
        // If refresh fails, clear cookies and redirect to login
        Cookies.remove('token');
        Cookies.remove('refreshToken');
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// Xero API service
export const xeroApi = {
  // Connect to Xero and get the authorization URL
  getAuthorizationUrl: () => api.get(API_ENDPOINTS.XERO_CONNECT),

  // Verify Xero connection status
  verifyConnection: () => api.get(API_ENDPOINTS.XERO_VERIFY),

  // Get Xero token for making direct API calls
  getToken: () => api.get(API_ENDPOINTS.XERO_TOKEN),

  // Get invoices from Xero
  getInvoices: () => api.get(API_ENDPOINTS.XERO_INVOICES),

  // Get contacts from Xero
  getContacts: () => api.get(API_ENDPOINTS.XERO_CONTACTS),

  // Get accounts from Xero
  getAccounts: () => api.get(API_ENDPOINTS.XERO_ACCOUNTS),

  // Get tax rates from Xero
  getTaxRates: () => api.get(API_ENDPOINTS.XERO_TAX_RATES),

  // Get invoice types from Xero
  getInvoiceTypes: () => api.get(API_ENDPOINTS.XERO_INVOICE_TYPES),

  // Disconnect Xero integration
  disconnect: () => api.post(API_ENDPOINTS.XERO_DISCONNECT),

  // Scan PDF invoices with Azure Form Recognizer
  scanInvoices: (formData) => api.post(API_ENDPOINTS.XERO_SCAN_INVOICES, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  }),

  // Get the invoice schema for validation
  getInvoiceSchema: () => api.get(API_ENDPOINTS.XERO_INVOICE_SCHEMA),

  // Create a new invoice in Xero
  createInvoice: (invoiceData) => api.post(API_ENDPOINTS.XERO_CREATE_INVOICE, invoiceData)
};

// Invoice Generator API
export const invoiceGeneratorAPI = {
  // Get all default templates
  getTemplates: () => {
    try {
      return api.get(API_ENDPOINTS.INVOICE_TEMPLATES);
    } catch (error) {
      console.error('Error fetching templates:', error);
      // Return mock data if API is not available
      return Promise.resolve({
        data: []
      });
    }
  },

  // Get specific template
  getTemplate: (id) => api.get(`${API_ENDPOINTS.INVOICE_TEMPLATES}${id}/`),

  // Custom templates
  getCustomTemplates: () => api.get(API_ENDPOINTS.CUSTOM_TEMPLATES),
  createCustomTemplate: (templateData) => api.post(API_ENDPOINTS.CUSTOM_TEMPLATES, templateData),
  updateCustomTemplate: (id, templateData) => api.put(`${API_ENDPOINTS.CUSTOM_TEMPLATES}${id}/`, templateData),
  deleteCustomTemplate: (id) => api.delete(`${API_ENDPOINTS.CUSTOM_TEMPLATES}${id}/`),
  markTemplateUsed: (id) => api.post(`${API_ENDPOINTS.CUSTOM_TEMPLATES}${id}/mark_used/`),

  // Company profile
  getCompanyProfile: () => {
    try {
      return api.get(API_ENDPOINTS.COMPANY_PROFILE);
    } catch (error) {
      console.error('Error fetching company profile:', error);
      return Promise.resolve({ data: {} });
    }
  },
  createCompanyProfile: (profileData) => api.post(API_ENDPOINTS.COMPANY_PROFILE, profileData),
  updateCompanyProfile: (profileData) => api.put(API_ENDPOINTS.COMPANY_PROFILE, profileData),

  // Template preview
  generatePreview: (previewData) => api.post(API_ENDPOINTS.GENERATE_TEMPLATE_PREVIEW, previewData),

  // Extract template information (existing)
  extractTemplateInfo: (formData) => api.post(API_ENDPOINTS.EXTRACT_TEMPLATE_INFO, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
};

export default api;